{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bun run vite", "build": "bun run vite build", "build:dev": "bun run vite build --mode development", "lint": "bun run eslint .", "preview": "bun run vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-form": "^1.12.1", "@tanstack/react-query": "^5.80.2", "@tanstack/react-router": "^1.120.15", "@tanstack/react-table": "^8.21.3", "caniuse-lite": "^1.0.30001721", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.512.0", "next-themes": "^0.3.0", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.25.0-beta.20250519T094321"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.8", "@tanstack/router-vite-plugin": "^1.120.15", "@types/node": "^22.5.5", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react-swc": "^3.10.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.5"}, "trustedDependencies": ["@swc/core"]}